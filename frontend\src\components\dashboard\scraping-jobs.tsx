import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import {
  AlertCircle,
  Award,
  BarChart2,
  Briefcase,
  Building2,
  CalendarClock,
  CheckCircle2,
  ChevronDown,
  ChevronRight,
  Clock,
  DollarSign,
  FileDown,
  FileSpreadsheet,
  FileText,
  Loader,
  Loader2,
  MapPin,
  RefreshCw,
  Search,
  Star,
  Target,
  ThumbsDown,
  ThumbsUp,
  Timer,
  Upload,
  Users,
  XCircle,
} from "lucide-react";
import React, { useCallback, useState } from "react";
import { toast } from "sonner";

// API base URL - adjust this based on your backend URL
const API_BASE_URL = "http://localhost:8080";

interface CompanyEntity {
  id: number;
  name: string;
  // Clutch fields
  clutchStatus: boolean;
  clutchRating?: number;
  clutchReviewsCount?: number;
  clutchLocation?: string;
  clutchEmployees?: string;
  clutchHourlyRate?: string;
  clutchMinProjectSize?: string;
  clutchCompanyOverview?: string;
  clutchAllServices?: string;
  clutchAllIndustries?: string;
  // Goodfirms fields
  goodfirmsStatus: boolean;
  goodfirmsRating?: number;
  goodfirmsReviewsCount?: number;
  goodfirmsLocation?: string;
  goodfirmsEmployees?: string;
  goodfirmsServices?: string;
  goodfirmsClientLikes?: string;
  goodfirmsClientDislikes?: string;
  // Glassdoor fields
  glassdoorStatus: boolean;
  glassdoorRating?: number;
  glassdoorReviewsCount?: number;
  glassdoorEmployeeSatisfaction?: number;
  glassdoorPros?: string;
  glassdoorCons?: string;
  glassdoorCategoryRatings?: string;
  glassdoorRatingsDistribution?: string;
  // LinkedIn fields
  linkedinStatus: boolean;
  linkedinFollowers?: number;
  linkedinEmployeesCount?: number;
  linkedinIndustry?: string;
  // Website fields
  websiteStatus: boolean;
  websiteDescription?: string;
  websiteFocusStatement?: string;
  websiteFoundedYear?: string;
  websiteEmployeeSize?: string;
  websiteHeadquartersLocation?: string;
  websiteServices?: string;
  websiteIndustries?: string;
  // General fields
  dataCompletenessScore?: number;
  lastScraped?: string;
}

interface Job {
  id: number;
  name: string;
  status: "QUEUE" | "SCHEDULED" | "IN_PROGRESS" | "SUCCESS" | "FAILED";
  createdAt: string;
  updatedAt: string;
  nextScheduledTime?: string;
  lastProcessedTime?: string;
  glassdoor?: string;
  clutch?: string;
  goodfirms?: string;
  linkedin?: string;
  website?: string;
  retryCount: number;
  maxRetries: number;
  createdBy?: string;
  lastModifiedBy?: string;
  companyEntity?: CompanyEntity;
  csvUploadId?: string;
}

interface CsvUpload {
  id: string;
  fileName: string;
  uploadedAt: string;
  totalJobs: number;
  completedJobs: number;
  failedJobs: number;
  inProgressJobs: number;
}

const statusConfig = {
  QUEUE: {
    icon: <Timer className="h-4 w-4 text-gray-500" />,
    label: "Queue",
    color: "bg-gray-100 text-gray-800",
  },
  SCHEDULED: {
    icon: <CalendarClock className="h-4 w-4 text-blue-500" />,
    label: "Scheduled",
    color: "bg-blue-100 text-blue-800",
  },
  IN_PROGRESS: {
    icon: <Loader className="h-4 w-4 animate-spin text-yellow-500" />,
    label: "In Progress",
    color: "bg-yellow-100 text-yellow-800",
  },
  SUCCESS: {
    icon: <CheckCircle2 className="h-4 w-4 text-green-500" />,
    label: "Success",
    color: "bg-green-100 text-green-800",
  },
  FAILED: {
    icon: <XCircle className="h-4 w-4 text-red-500" />,
    label: "Failed",
    color: "bg-red-100 text-red-800",
  },
};

const formatDate = (dateString?: string) => {
  if (!dateString) return "N/A";
  try {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    }).format(new Date(dateString));
  } catch (error) {
    console.error("Error formatting date:", dateString, error);
    return "Invalid Date";
  }
};

// Helper function to format JSON string
const formatJsonString = (jsonString: string | undefined): string => {
  if (!jsonString) return "";
  try {
    // If it's a JSON string, parse and stringify it with formatting
    const parsed = JSON.parse(jsonString);
    return JSON.stringify(parsed, null, 2);
  } catch {
    // If it's not JSON, return as is
    return jsonString;
  }
};

interface JsonViewProps {
  label: string;
  content?: string;
  icon?: React.ReactNode;
}

const JsonView = React.memo(function JsonView({
  label,
  content,
  icon,
}: JsonViewProps) {
  if (!content) return null;

  return (
    <div className="space-y-1">
      <div className="flex items-center gap-2 text-sm font-medium">
        {icon}
        <span>{label}</span>
      </div>
      <Textarea
        value={formatJsonString(content)}
        disabled
        className="font-mono text-xs h-[100px] resize-none bg-muted/50"
      />
    </div>
  );
});

const ScraperStatusBadge = React.memo(function ScraperStatusBadge({
  status,
  source,
  jobStatus,
}: {
  status: boolean | null | undefined;
  source: string;
  jobStatus: string;
}) {
  // If job is in PROCESSING or SCHEDULED state, show pending status
  if (
    jobStatus === "IN_PROGRESS" ||
    jobStatus === "SCHEDULED" ||
    jobStatus === "QUEUE"
  ) {
    return (
      <Badge variant="outline" className="gap-1">
        <Clock className="h-3 w-3 text-blue-500" />
        {source} ({jobStatus === "IN_PROGRESS" ? "Processing" : "Awaiting"})
      </Badge>
    );
  }

  // For completed jobs, show actual scraper status
  if (status === true) {
    return (
      <Badge variant="default" className="bg-green-500 gap-1">
        <CheckCircle2 className="h-3 w-3" />
        {source} (Done)
      </Badge>
    );
  }

  if (status === false) {
    return (
      <Badge variant="destructive" className="gap-1">
        <XCircle className="h-3 w-3" />
        {source} (Failed)
      </Badge>
    );
  }

  // Default case (should not happen, but just in case)
  return (
    <Badge variant="outline" className="gap-1">
      <Clock className="h-3 w-3 text-blue-500" />
      {source} (Pending)
    </Badge>
  );
});

const CompanyDetails = React.memo(function CompanyDetails({
  company,
  jobStatus,
}: {
  company: CompanyEntity;
  jobStatus: string;
}) {
  if (!company) return null;

  return (
    <div className="space-y-6 p-4">
      {/* Scraper Status Section */}
      <div>
        <h4 className="text-sm font-semibold mb-2">Scraper Status</h4>
        <div className="flex flex-wrap gap-2">
          <ScraperStatusBadge
            status={company.clutchStatus}
            source="Clutch"
            jobStatus={jobStatus}
          />
          <ScraperStatusBadge
            status={company.goodfirmsStatus}
            source="GoodFirms"
            jobStatus={jobStatus}
          />
          <ScraperStatusBadge
            status={company.glassdoorStatus}
            source="Glassdoor"
            jobStatus={jobStatus}
          />
          <ScraperStatusBadge
            status={company.linkedinStatus}
            source="LinkedIn"
            jobStatus={jobStatus}
          />
          <ScraperStatusBadge
            status={company.websiteStatus}
            source="Website"
            jobStatus={jobStatus}
          />
        </div>
      </div>

      {/* Data Completeness Score */}
      {company.dataCompletenessScore !== undefined && (
        <div className="flex items-center gap-4">
          <div className="flex-1">
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm font-medium">Data Completeness</span>
              <span className="text-sm font-medium">
                {Math.round(company.dataCompletenessScore)}%
              </span>
            </div>
            <Progress value={company.dataCompletenessScore} className="h-2" />
          </div>
          <span className="text-xs text-muted-foreground">
            Last updated: {formatDate(company.lastScraped)}
          </span>
        </div>
      )}

      {/* Detailed Information Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Clutch Section */}
        {company.clutchStatus && (
          <div className="rounded-lg border p-4 space-y-3 break-words min-w-0">
            <h5 className="font-medium flex items-center gap-2">
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                role="img"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M16.592 9.196s-.354-3.298-3.627-3.39c-3.274-.09-4.955 2.474-4.955 6.14 0 3.665 1.858 6.597 5.045 6.597 3.184 0 3.538-3.665 3.538-3.665l6.104.365s.36 3.31-2.196 5.836c-2.552 2.524-5.69 2.937-7.876 2.92-2.19-.016-5.226.035-8.16-2.97-2.938-3.01-3.436-5.93-3.436-8.8 0-2.87.556-6.67 4.047-9.55C7.444.72 9.849 0 12.254 0c10.042 0 10.717 9.26 10.717 9.26z" />
              </svg>
              Clutch Data
            </h5>
            <div className="space-y-2 text-sm w-full max-w-full">
              {company.clutchRating && (
                <div className="flex items-start gap-2 w-full max-w-full">
                  <Star className="h-4 w-4 text-yellow-500" />
                  <div className="min-w-0 break-words !whitespace-normal">
                    Rating: {company.clutchRating.toFixed(1)} (
                    {company.clutchReviewsCount} reviews)
                  </div>
                </div>
              )}
              {company.clutchLocation && (
                <div className="flex items-start gap-2 w-full max-w-full">
                  <MapPin className="h-4 w-4" />
                  <div className="min-w-0 break-words !whitespace-normal">
                    {company.clutchLocation}
                  </div>
                </div>
              )}
              {company.clutchEmployees && (
                <div className="flex items-start gap-2 w-full max-w-full">
                  <Users className="h-4 w-4" />
                  <div className="min-w-0 break-words !whitespace-normal">
                    {company.clutchEmployees} employees
                  </div>
                </div>
              )}
              {company.clutchHourlyRate && (
                <div className="flex items-start gap-2 w-full max-w-full">
                  <DollarSign className="h-4 w-4" />
                  <div className="min-w-0 break-words !whitespace-normal">
                    Rate: {company.clutchHourlyRate}
                  </div>
                </div>
              )}
              <JsonView
                label="Services"
                content={company.clutchAllServices}
                icon={<Briefcase className="h-4 w-4" />}
              />
              <JsonView
                label="Industries"
                content={company.clutchAllIndustries}
                icon={<Building2 className="h-4 w-4" />}
              />
            </div>
          </div>
        )}

        {/* Goodfirms Section */}
        {company.goodfirmsStatus && (
          <div className="rounded-lg border p-4 space-y-3 break-words min-w-0">
            <h5 className="font-medium flex items-center gap-2">
              <svg
                fill="#000000"
                width="16"
                height="16"
                viewBox="0 0 14 14"
                role="img"
                focusable="false"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="m 1.000845,8.6142 c 9.6e-4,-4.9857 -0.011,-4.8049 0.36753,-5.5718 0.46453,-0.9412 1.33106,-1.6372 2.48046,-1.9922 0.31054,-0.096 0.4558,-0.099 4.65895,-0.099 l 4.33841,0 0,1.6535 0,1.6536 -3.92564,0 c -3.18544,0 -3.95574,0.013 -4.08525,0.067 -0.26656,0.1114 -0.46644,0.383 -0.50288,0.6834 -0.0175,0.144 -0.0248,2.011 -0.0162,4.1489 l 0.0155,3.887 -1.66588,0 -1.66587,0 z m 4.24403,2.7041 0,-1.7292 2.08543,0 c 1.6327,0 2.10375,-0.015 2.16986,-0.066 0.13463,-0.1023 0.18704,-0.3057 0.18704,-0.7257 l 0,-0.3917 -2.22116,0 -2.22117,0 0,-1.3589 c 0,-1.0815 0.0148,-1.3943 0.0723,-1.5319 0.1368,-0.3275 0.01,-0.3175 4.05051,-0.3175 l 3.63234,0 -0.0177,2.5296 c -0.0173,2.4592 -0.0209,2.54 -0.12943,2.8999 -0.30587,1.0138 -1.05314,1.7915 -2.14346,2.2307 l -0.40517,0.1632 -2.52966,0.014 -2.52966,0.014 0,-1.7293 z" />
              </svg>
              GoodFirms Data
            </h5>
            <div className="space-y-2 text-sm w-full max-w-full">
              {company.goodfirmsRating && (
                <div className="flex items-start gap-2 w-full max-w-full">
                  <Star className="h-4 w-4 text-yellow-500 flex-shrink-0 mt-0.5" />
                  <div className="min-w-0 break-words !whitespace-normal">
                    Rating: {company.goodfirmsRating.toFixed(1)} (
                    {company.goodfirmsReviewsCount} reviews)
                  </div>
                </div>
              )}
              {company.goodfirmsLocation && (
                <div className="flex items-start gap-2 w-full max-w-full">
                  <MapPin className="h-4 w-4 flex-shrink-0 mt-0.5" />
                  <div className="min-w-0 break-words !whitespace-normal">
                    {company.goodfirmsLocation}
                  </div>
                </div>
              )}
              {company.goodfirmsEmployees && (
                <div className="flex items-start gap-2 w-full max-w-full">
                  <Users className="h-4 w-4 flex-shrink-0 mt-0.5" />
                  <div className="min-w-0 break-words !whitespace-normal">
                    {company.goodfirmsEmployees} employees
                  </div>
                </div>
              )}
              <JsonView
                label="Services"
                content={company.goodfirmsServices}
                icon={<Briefcase className="h-4 w-4" />}
              />
              <JsonView
                label="Client Likes"
                content={company.goodfirmsClientLikes}
                icon={<ThumbsUp className="h-4 w-4" />}
              />
              <JsonView
                label="Client Dislikes"
                content={company.goodfirmsClientDislikes}
                icon={<ThumbsDown className="h-4 w-4" />}
              />
            </div>
          </div>
        )}

        {/* Glassdoor Section */}
        {company.glassdoorStatus && (
          <div className="rounded-lg border p-4 space-y-3 break-words min-w-0">
            <h5 className="font-medium flex items-center gap-2">
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                role="img"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M17.144 20.572H3.43A3.427 3.427 0 0 0 6.856 24h10.286a3.428 3.428 0 0 0 3.428-3.428V6.492a.123.123 0 0 0-.124-.125h-3.18a.125.125 0 0 0-.123.126v14.08zm0-20.572a3.429 3.429 0 0 1 3.427 3.43H6.858v14.078a.126.126 0 0 1-.125.125H3.554a.125.125 0 0 1-.125-.125V3.428A3.429 3.429 0 0 1 6.856 0h10.287" />
              </svg>
              Glassdoor Data
            </h5>
            <div className="space-y-2 text-sm w-full max-w-full">
              {company.glassdoorRating && (
                <div className="flex items-start gap-2 w-full max-w-full">
                  <Star className="h-4 w-4 text-yellow-500" />
                  <div className="min-w-0 break-words !whitespace-normal">
                    Rating: {company.glassdoorRating.toFixed(1)} (
                    {company.glassdoorReviewsCount} reviews)
                  </div>
                </div>
              )}
              {company.glassdoorEmployeeSatisfaction && (
                <div className="flex items-start gap-2 w-full max-w-full">
                  <Award className="h-4 w-4" />
                  <div className="min-w-0 break-words !whitespace-normal">
                    Employee Satisfaction:{" "}
                    {company.glassdoorEmployeeSatisfaction.toFixed(1)}
                  </div>
                </div>
              )}
              <JsonView
                label="Category Ratings"
                content={company.glassdoorCategoryRatings}
                icon={<BarChart2 className="h-4 w-4" />}
              />
              <JsonView
                label="Pros"
                content={company.glassdoorPros}
                icon={<ThumbsUp className="h-4 w-4" />}
              />
              <JsonView
                label="Cons"
                content={company.glassdoorCons}
                icon={<ThumbsDown className="h-4 w-4" />}
              />
            </div>
          </div>
        )}

        {/* LinkedIn Section */}
        {company.linkedinStatus && (
          <div className="rounded-lg border p-4 space-y-3 break-words min-w-0">
            <h5 className="font-medium flex items-center gap-2">
              <svg
                width="18"
                height="18"
                viewBox="0 0 16 16"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M3 1a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V3a2 2 0 00-2-2H3zm1.102 4.297a1.195 1.195 0 100-2.39 1.195 1.195 0 000 2.39zm1 7.516V6.234h-2v6.579h2zM6.43 6.234h2v.881c.295-.462.943-1.084 2.148-1.084 1.438 0 2.219.953 2.219 2.766 0 .087.008.484.008.484v3.531h-2v-3.53c0-.485-.102-1.438-1.18-1.438-1.079 0-1.17 1.198-1.195 1.982v2.986h-2V6.234z"
                  fill="#000000"
                />
              </svg>
              LinkedIn Data
            </h5>
            <div className="space-y-2 text-sm w-full max-w-full">
              {company.linkedinFollowers && (
                <div className="flex items-start gap-2 w-full max-w-full">
                  <Users className="h-4 w-4" />
                  <div className="min-w-0 break-words !whitespace-normal">
                    {company.linkedinFollowers.toLocaleString()} followers
                  </div>
                </div>
              )}
              {company.linkedinEmployeesCount && (
                <div className="flex items-start gap-2 w-full max-w-full">
                  <Building2 className="h-4 w-4" />
                  <div className="min-w-0 break-words !whitespace-normal">
                    {company.linkedinEmployeesCount.toLocaleString()} employees
                  </div>
                </div>
              )}
              {company.linkedinIndustry && (
                <div className="flex items-start gap-2 w-full max-w-full">
                  <Briefcase className="h-4 w-4" />
                  <div className="min-w-0 break-words !whitespace-normal">
                    Industry: {company.linkedinIndustry}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Website Section */}
        {company.websiteStatus && (
          <div className="rounded-lg border p-4 space-y-3 break-words min-w-0">
            <h5 className="font-medium flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Website Data
            </h5>
            <div className="space-y-2 text-sm w-full max-w-full">
              {company.websiteFoundedYear && (
                <div className="flex items-start gap-2 w-full max-w-full">
                  <Clock className="h-4 w-4" />
                  <div className="min-w-0 break-words !whitespace-normal">
                    Founded: {company.websiteFoundedYear}
                  </div>
                </div>
              )}
              {company.websiteHeadquartersLocation && (
                <div className="flex items-start gap-2 w-full max-w-full">
                  <MapPin className="h-4 w-4" />
                  <div className="min-w-0 break-words !whitespace-normal">
                    HQ: {company.websiteHeadquartersLocation}
                  </div>
                </div>
              )}
              {company.websiteEmployeeSize && (
                <div className="flex items-start gap-2 w-full max-w-full">
                  <Users className="h-4 w-4" />
                  <div className="min-w-0 break-words !whitespace-normal">
                    {company.websiteEmployeeSize} employees
                  </div>
                </div>
              )}
              <JsonView
                label="Description"
                content={company.websiteDescription}
                icon={<FileText className="h-4 w-4" />}
              />
              <JsonView
                label="Focus Statement"
                content={company.websiteFocusStatement}
                icon={<Target className="h-4 w-4" />}
              />
              <JsonView
                label="Services"
                content={company.websiteServices}
                icon={<Briefcase className="h-4 w-4" />}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

const JobStatusDisplay = ({ job }: { job: Job }) => {
  const { status, companyEntity } = job;

  if (
    status === "QUEUE" ||
    status === "SCHEDULED" ||
    status === "IN_PROGRESS"
  ) {
    return (
      <div className="flex items-center gap-2">
        {statusConfig[status]?.icon}
        <Badge className={statusConfig[status]?.color}>
          {statusConfig[status]?.label || status}
        </Badge>
      </div>
    );
  }

  if (companyEntity) {
    const scrapers = [
      companyEntity.clutchStatus,
      companyEntity.goodfirmsStatus,
      companyEntity.glassdoorStatus,
      companyEntity.linkedinStatus,
      companyEntity.websiteStatus,
    ];
    const successCount = scrapers.filter((s) => s === true).length;
    const failedCount = scrapers.filter((s) => s === false).length;

    const statusBadges = [];
    if (successCount > 0) {
      statusBadges.push(
        <Badge
          key="success"
          variant="default"
          className="bg-green-100 text-green-800 gap-1"
        >
          <CheckCircle2 className="h-4 w-4 text-green-500" /> {successCount}
        </Badge>
      );
    }
    if (failedCount > 0) {
      statusBadges.push(
        <Badge key="failed" variant="destructive" className="gap-1">
          <XCircle className="h-4 w-4 text-white" /> {failedCount}
        </Badge>
      );
    }

    if (statusBadges.length > 0) {
      return (
        <div className="flex flex-row gap-1 items-start">{statusBadges}</div>
      );
    }
  }

  return (
    <div className="flex items-center gap-2">
      {statusConfig[status]?.icon}
      <Badge className={statusConfig[status]?.color}>
        {statusConfig[status]?.label || status}
      </Badge>
    </div>
  );
};

const CsvUploadCard = React.memo(function CsvUploadCard({
  upload,
  onDownload,
  onToggle,
  isExpanded,
  jobs,
}: {
  upload: CsvUpload;
  onDownload: () => void;
  onToggle: () => void;
  isExpanded: boolean;
  jobs: Job[];
}) {
  const [expandedJobs, setExpandedJobs] = useState<Set<number>>(new Set());
  const totalProgress = Math.round(
    (upload.completedJobs / upload.totalJobs) * 100
  );
  const hasFailures = upload.failedJobs > 0;
  const isComplete = upload.completedJobs === upload.totalJobs;
  const inProgress = upload.inProgressJobs > 0;

  const toggleJob = (jobId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    const newExpandedJobs = new Set(expandedJobs);
    if (newExpandedJobs.has(jobId)) {
      newExpandedJobs.delete(jobId);
    } else {
      newExpandedJobs.add(jobId);
    }
    setExpandedJobs(newExpandedJobs);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      <div className="p-4">
        {/* Header Row with integrated progress */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <div className="p-1.5 bg-primary/10 rounded-md shrink-0">
              <FileSpreadsheet className="h-4 w-4 text-primary" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-3">
                <div className="min-w-0">
                  <h3 className="font-medium text-base truncate">
                    {upload.fileName}
                  </h3>
                  <p className="text-xs text-muted-foreground">
                    {formatDate(upload.uploadedAt)}
                  </p>
                </div>
                <div className="flex items-center gap-2 ml-auto mr-4">
                  <Progress value={totalProgress} className="h-1.5 w-24" />
                  <span className="text-xs font-medium text-muted-foreground whitespace-nowrap min-w-[35px]">
                    {totalProgress}%
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant={isComplete ? "default" : "outline"}
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onDownload();
              }}
              disabled={!isComplete}
              className="h-8 px-3 text-xs"
            >
              <FileDown className="h-3 w-3 mr-1.5" />
              {isComplete ? "Download" : "Processing"}
            </Button>
          </div>
        </div>

        {/* Compact Stats with labels */}
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1.5">
              <Clock className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">Total</span>
              <span className="font-medium">{upload.totalJobs}</span>
            </div>
            <div className="flex items-center gap-1.5">
              <CheckCircle2 className="h-3 w-3 text-green-500" />
              <span className="text-green-600">Done</span>
              <span className="font-medium text-green-600">
                {upload.completedJobs}
              </span>
            </div>
            {upload.inProgressJobs > 0 && (
              <div className="flex items-center gap-1.5">
                <Loader className="h-3 w-3 text-blue-500 animate-spin" />
                <span className="text-blue-600">Active</span>
                <span className="font-medium text-blue-600">
                  {upload.inProgressJobs}
                </span>
              </div>
            )}
            {hasFailures && (
              <div className="flex items-center gap-1.5">
                <XCircle className="h-3 w-3 text-red-500" />
                <span className="text-red-600">Failed</span>
                <span className="font-medium text-red-600">
                  {upload.failedJobs}
                </span>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            {inProgress && (
              <span className="text-muted-foreground flex items-center gap-1">
                <Loader className="h-3 w-3 animate-spin" />
                Processing
              </span>
            )}
            <Button
              variant="ghost"
              size="sm"
              className="h-6 px-2 text-xs"
              onClick={onToggle}
            >
              {isExpanded ? (
                <>
                  <ChevronDown className="h-3 w-3 mr-1" />
                  Hide
                </>
              ) : (
                <>
                  <ChevronRight className="h-3 w-3 mr-1" />
                  Details
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Error Alert - Compact */}
        {hasFailures && !isExpanded && (
          <div className="flex items-center gap-1 text-xs text-red-500 mt-2 p-2 bg-red-50 rounded-md">
            <AlertCircle className="h-3 w-3" />
            <span>{upload.failedJobs} failed</span>
          </div>
        )}
      </div>

      {isExpanded && (
        <div className="border-t bg-gray-50/50">
          <div className="p-4">
            <Table>
              <TableHeader>
                <TableRow className="hover:bg-transparent">
                  <TableHead className="h-8 text-xs">Company</TableHead>
                  <TableHead className="h-8 text-xs">Status</TableHead>
                  <TableHead className="h-8 text-xs">Queued</TableHead>
                  <TableHead className="h-8 text-xs">Scheduled</TableHead>
                  <TableHead className="h-8 text-xs">Processed</TableHead>
                  <TableHead className="h-8 w-[40px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {jobs.map((job) => (
                  <React.Fragment key={job.id}>
                    <TableRow
                      className={cn(
                        "hover:bg-muted/50 h-10",
                        job.status === "FAILED" && "bg-red-50/50"
                      )}
                    >
                      <TableCell className="font-medium text-sm py-2 truncate max-w-[200px]">
                        {job.name}
                      </TableCell>
                      <TableCell className="py-2">
                        <JobStatusDisplay job={job} />
                      </TableCell>
                      <TableCell className="text-xs text-muted-foreground py-2">
                        {formatDate(job.createdAt)}
                      </TableCell>
                      <TableCell className="text-xs text-muted-foreground py-2">
                        {formatDate(job.nextScheduledTime)}
                      </TableCell>
                      <TableCell className="text-xs text-muted-foreground py-2">
                        {formatDate(job.lastProcessedTime)}
                      </TableCell>
                      <TableCell className="py-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={(e) => toggleJob(job.id, e)}
                        >
                          {expandedJobs.has(job.id) ? (
                            <ChevronDown className="h-3 w-3" />
                          ) : (
                            <ChevronRight className="h-3 w-3" />
                          )}
                        </Button>
                      </TableCell>
                    </TableRow>
                    {expandedJobs.has(job.id) && (
                      <TableRow>
                        <TableCell colSpan={6} className="p-0">
                          <div className="border-t">
                            {job.companyEntity ? (
                              <CompanyDetails
                                company={job.companyEntity}
                                jobStatus={job.status}
                              />
                            ) : (
                              <div className="p-6 text-center text-muted-foreground">
                                <Loader2 className="h-5 w-5 animate-spin mx-auto mb-2" />
                                <p className="text-sm">
                                  Scraping in progress...
                                </p>
                              </div>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      )}
    </div>
  );
});

export function ScrapingJobs() {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [csvUploads, setCsvUploads] = useState<CsvUpload[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [expandedUploads, setExpandedUploads] = useState<Set<string>>(
    new Set()
  );
  const [searchTerm, setSearchTerm] = useState("");

  const toggleUpload = (uploadId: string) => {
    const newExpandedUploads = new Set(expandedUploads);
    if (newExpandedUploads.has(uploadId)) {
      newExpandedUploads.delete(uploadId);
    } else {
      newExpandedUploads.add(uploadId);
    }
    setExpandedUploads(newExpandedUploads);
  };

  const fetchJobs = useCallback(async () => {
    setIsLoading(true);
    try {
      const responses = await Promise.all([
        fetch(`${API_BASE_URL}/api/scrapping/queue`),
        fetch(`${API_BASE_URL}/api/scrapping/scheduled`),
        fetch(`${API_BASE_URL}/api/scrapping/processing`),
        fetch(`${API_BASE_URL}/api/scrapping/completed`),
        fetch(`${API_BASE_URL}/api/scrapping/failed`),
      ]);

      // Check if any response is not ok
      for (const response of responses) {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
      }

      const [queued, scheduled, processing, completed, failed] =
        await Promise.all(responses.map((r) => r.json()));

      // Deduplicate jobs, keeping the most recent status
      const jobMap = new Map<number, Job>();
      const allJobs = [
        ...queued,
        ...scheduled,
        ...processing,
        ...completed,
        ...failed,
      ];

      allJobs.forEach((job) => {
        const existingJob = jobMap.get(job.id);
        if (
          !existingJob ||
          new Date(job.updatedAt) > new Date(existingJob.updatedAt)
        ) {
          jobMap.set(job.id, job);
        }
      });

      const sortedJobs = Array.from(jobMap.values())
        .sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )
        .map((job) => ({
          ...job,
          // Mark as IN_PROGRESS if the job is currently being processed
          status: processing.some((p: Job) => p.id === job.id)
            ? "IN_PROGRESS"
            : job.status,
        }));

      setJobs(sortedJobs);
    } catch (error) {
      console.error("Error fetching jobs:", error);
      toast.error(
        error instanceof Error && error.message.includes("Failed to fetch")
          ? "Unable to connect to the server. Please make sure the backend is running."
          : "Failed to fetch jobs. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchCsvUploads = useCallback(async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/scrapping/csv-uploads`);
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      const uploads = await response.json();
      const correctedUploads = uploads.map((upload: CsvUpload) => {
        const { totalJobs, completedJobs, failedJobs, inProgressJobs } = upload;
        if (inProgressJobs === 0 && totalJobs > completedJobs + failedJobs) {
          return {
            ...upload,
            inProgressJobs: totalJobs - completedJobs - failedJobs,
          };
        }
        return upload;
      });
      setCsvUploads(correctedUploads);
    } catch (error) {
      console.error("Error fetching CSV uploads:", error);
      toast.error("Failed to fetch CSV uploads");
    }
  }, []);

  const handleDownloadCsv = useCallback(
    async (csvUploadId: string, fileName: string) => {
      try {
        const response = await fetch(
          `${API_BASE_URL}/api/scrapping/download/${csvUploadId}`
        );
        if (!response.ok)
          throw new Error(`HTTP error! status: ${response.status}`);

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = fileName.replace(".csv", "_scraped.csv");
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast.success("CSV file downloaded successfully");
      } catch (error) {
        console.error("Error downloading CSV:", error);
        toast.error("Failed to download CSV file");
      }
    },
    []
  );

  const handleFileUpload = useCallback(
    async (file: File) => {
      const formData = new FormData();
      formData.append("file", file);

      try {
        const response = await fetch(`${API_BASE_URL}/api/scrapping/upload`, {
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => null);
          throw new Error(errorData?.error || "Upload failed");
        }

        const result = await response.json();
        toast.success(
          `CSV file uploaded successfully. ${result.successCount} jobs created.`
        );

        // Refresh both jobs and CSV uploads
        fetchJobs();
        fetchCsvUploads();
      } catch (error) {
        console.error("Error uploading file:", error);
        toast.error(
          error instanceof Error && error.message.includes("Failed to fetch")
            ? "Unable to connect to the server. Please make sure the backend is running."
            : "Failed to upload file. Please try again."
        );
      }
    },
    [fetchJobs, fetchCsvUploads]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragging(false);

      const file = e.dataTransfer.files[0];
      if (!file || !file.name.endsWith(".csv")) {
        toast.error("Please upload a valid CSV file.");
        return;
      }

      handleFileUpload(file);
    },
    [handleFileUpload]
  );

  const handleFileSelect = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (!file) return;

      if (!file.name.endsWith(".csv")) {
        toast.error("Please upload a valid CSV file.");
        return;
      }

      handleFileUpload(file);
    },
    [handleFileUpload]
  );

  React.useEffect(() => {
    fetchJobs();
    fetchCsvUploads();
    // Set up polling every 30 seconds
    const interval = setInterval(() => {
      fetchJobs();
      fetchCsvUploads();
    }, 30000);
    return () => clearInterval(interval);
  }, [fetchJobs, fetchCsvUploads]);

  const filteredCsvUploads = csvUploads.filter((upload) =>
    upload.fileName.toLowerCase().includes(searchTerm.trim().toLowerCase())
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Scraping Jobs</CardTitle>
            <CardDescription>
              Upload CSV files to scrape company information from multiple
              sources.
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={fetchJobs}>
            <RefreshCw
              className={cn("h-4 w-4 mr-2", isLoading && "animate-spin")}
            />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div
          className={cn(
            "border-2 border-dashed rounded-lg p-4 text-center transition-colors",
            isDragging
              ? "border-primary bg-primary/5"
              : "border-muted-foreground/25",
            "hover:border-primary hover:bg-primary/5"
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="mx-auto flex max-w-[420px] flex-col items-center justify-center text-center">
            <Upload className="h-8 w-8 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold">Drop your CSV file here</h3>
            <p className="text-sm text-muted-foreground mb-4">
              or click to browse for a file
            </p>
            <input
              type="file"
              accept=".csv"
              className="hidden"
              onChange={handleFileSelect}
              id="file-upload"
            />
            <Button variant="secondary" asChild>
              <label htmlFor="file-upload" className="cursor-pointer">
                Choose File
              </label>
            </Button>
          </div>
        </div>

        {csvUploads.length > 0 && (
          <div className="mt-8 space-y-6">
            <div className="flex items-center justify-between gap-4">
              <h3 className="text-lg font-semibold">Recent Data Scraping</h3>
              <div className="relative w-full max-w-sm">
                <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by filename..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="h-9 pl-8"
                />
              </div>
            </div>
            {filteredCsvUploads.map((upload) => (
              <CsvUploadCard
                key={upload.id}
                upload={upload}
                onDownload={() => handleDownloadCsv(upload.id, upload.fileName)}
                onToggle={() => toggleUpload(upload.id)}
                isExpanded={expandedUploads.has(upload.id)}
                jobs={jobs.filter((job) => job.csvUploadId === upload.id)}
              />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
