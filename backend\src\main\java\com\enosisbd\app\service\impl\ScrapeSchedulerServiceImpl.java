package com.enosisbd.app.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

import org.springframework.stereotype.Service;

import com.enosisbd.app.config.SchedulingConfig;
import com.enosisbd.app.model.ScrapeJob;
import com.enosisbd.app.model.ScrapeStatus;
import com.enosisbd.app.service.FileBasedJobService;
import com.enosisbd.app.service.ScrapeSchedulerService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScrapeSchedulerServiceImpl implements ScrapeSchedulerService {

    private final FileBasedJobService fileBasedJobService;
    private final SchedulingConfig schedulingConfig;

    @Override
    public void scheduleReadyItems() {
        log.debug("Starting to schedule ready items...");

        LocalDateTime currentTime = LocalDateTime.now();
        List<ScrapeJob> readyItems = fileBasedJobService.getItemsReadyForScheduling();

        log.debug("Current time: {}, Found {} items ready for scheduling", currentTime, readyItems.size());

        if (readyItems.isEmpty()) {
            // Check if there are any items in QUEUE status at all
            List<ScrapeJob> queueItems = fileBasedJobService.getScrapeJobsByStatus(ScrapeStatus.QUEUE);
            log.debug("Total items in QUEUE status: {}", queueItems.size());

            if (!queueItems.isEmpty()) {
                log.debug("Queue items exist but not ready for scheduling:");
                queueItems.forEach(item ->
                    log.debug("Item ID: {}, Name: {}, Next Scheduled Time: {}",
                        item.getId(), item.getName(), item.getNextScheduledTime())
                );
            }

            log.debug("No items ready for scheduling");
            return;
        }

        log.info("Found {} items ready for scheduling", readyItems.size());

        // Check if we can schedule new items (no conflicts with processing items)
        List<ScrapeJob> processingItems = fileBasedJobService.getScrapeJobsByStatus(ScrapeStatus.IN_PROGRESS);
        if (processingItems.size() >= schedulingConfig.getMaxConcurrentProcessing()) {
            log.info("Maximum concurrent processing limit reached. Currently processing: {}. Skipping scheduling.", processingItems.size());
            return;
        }

        // Calculate next available scheduling slot
        LocalDateTime nextAvailableSlot = calculateNextAvailableSlot();
        log.debug("Next available slot calculated as: {}", nextAvailableSlot);

        for (ScrapeJob job : readyItems) {
            try {
                LocalDateTime oldScheduledTime = job.getNextScheduledTime();

                // Schedule each item with incremental time slots
                job.setNextScheduledTime(nextAvailableSlot);
                job.setStatus(ScrapeStatus.SCHEDULED);
                job.setLastModifiedBy("SCHEDULER");

                fileBasedJobService.saveScrapeJob(job);

                log.info("Scheduled scrape job {} (name: {}) from {} to {}",
                    job.getId(), job.getName(), oldScheduledTime, nextAvailableSlot);

                // Add random gap for next item
                int randomGap = schedulingConfig.getMinGapMinutes() +
                    ThreadLocalRandom.current().nextInt(schedulingConfig.getMaxAdditionalRandomMinutes());
                nextAvailableSlot = nextAvailableSlot.plusMinutes(randomGap);

                log.debug("Next item will be scheduled for: {} (gap: {} minutes)", nextAvailableSlot, randomGap);

            } catch (Exception e) {
                log.error("Failed to schedule job {}: {}", job.getId(), e.getMessage(), e);
            }
        }

        log.info("Completed scheduling {} items", readyItems.size());
    }

    @Override
    public void processScheduledItems() {
        log.debug("Starting to process scheduled items...");

        LocalDateTime currentTime = LocalDateTime.now();
        List<ScrapeJob> readyForProcessing = fileBasedJobService.getItemsReadyForProcessing();

        log.debug("Current time: {}, Found {} items ready for processing", currentTime, readyForProcessing.size());

        if (readyForProcessing.isEmpty()) {
            // Check if there are any scheduled items at all
            List<ScrapeJob> scheduledItems = fileBasedJobService.getScrapeJobsByStatus(ScrapeStatus.SCHEDULED);
            log.debug("Total items in SCHEDULED status: {}", scheduledItems.size());

            if (!scheduledItems.isEmpty()) {
                log.debug("Scheduled items exist but not ready for processing:");
                scheduledItems.forEach(item ->
                    log.debug("Item ID: {}, Name: {}, Scheduled Time: {}, Time until ready: {} minutes",
                        item.getId(), item.getName(), item.getNextScheduledTime(),
                        java.time.Duration.between(currentTime, item.getNextScheduledTime()).toMinutes())
                );
            }

            log.debug("No items ready for processing");
            return;
        }

        // Check current processing limit
        List<ScrapeJob> currentlyProcessing = fileBasedJobService.getScrapeJobsByStatus(ScrapeStatus.IN_PROGRESS);
        int availableSlots = schedulingConfig.getMaxConcurrentProcessing() - currentlyProcessing.size();

        log.debug("Currently processing: {}, Available slots: {}", currentlyProcessing.size(), availableSlots);

        if (availableSlots <= 0) {
            log.info("No available processing slots. Currently processing: {}", currentlyProcessing.size());
            return;
        }

        // Process items up to available slots
        int itemsToProcess = Math.min(availableSlots, readyForProcessing.size());
        List<ScrapeJob> itemsForProcessing = readyForProcessing.subList(0, itemsToProcess);

        log.info("Processing {} items", itemsForProcessing.size());

        for (ScrapeJob job : itemsForProcessing) {
            try {
                log.info("Starting to process job {} (name: {})", job.getId(), job.getName());
                startProcessing(job);
            } catch (Exception e) {
                log.error("Failed to start processing job {}: {}", job.getId(), e.getMessage());
                markAsFailed(job, "Failed to start processing: " + e.getMessage());
            }
        }
    }

    @Override
    public void calculateAndSetNextScheduledTime(ScrapeJob job) {
        LocalDateTime nextSlot = calculateNextAvailableSlot();
        job.setNextScheduledTime(nextSlot);
        log.debug("Calculated next scheduled time for job {} (name: {}): {}", job.getId(), job.getName(), nextSlot);
    }

    @Override
    public void startProcessing(ScrapeJob job) {
        log.info("Starting processing for scrape job: {} (name: {})", job.getId(), job.getName());

        job.setStatus(ScrapeStatus.IN_PROGRESS);
        job.setLastProcessedTime(LocalDateTime.now());
        job.setLastModifiedBy("PROCESSOR");

        fileBasedJobService.saveScrapeJob(job);

        // Note: Flowable workflow has been removed in file-based approach
        // The actual scraping logic should be handled by other services
        log.info("Scrape job {} (name: {}) marked as IN_PROGRESS", job.getId(), job.getName());
    }

    @Override
    public void markAsCompleted(ScrapeJob job) {
        log.info("Marking scrape job {} (name: {}) as completed", job.getId(), job.getName());

        job.setStatus(ScrapeStatus.SUCCESS);
        job.setLastModifiedBy("PROCESSOR");
        job.setRetryCount(0); // Reset retry count on success

        fileBasedJobService.saveScrapeJob(job);
    }

    @Override
    public void markAsFailed(ScrapeJob job, String error) {
        log.warn("Marking scrape job {} (name: {}) as failed: {}", job.getId(), job.getName(), error);

        job.setRetryCount(job.getRetryCount() + 1);
        job.setLastModifiedBy("PROCESSOR");

        if (job.getRetryCount() >= job.getMaxRetries()) {
            // Max retries reached, mark as permanently failed
            job.setStatus(ScrapeStatus.FAILED);
            log.error("Scrape job {} (name: {}) has reached maximum retries and is marked as FAILED",
                job.getId(), job.getName());
        } else {
            // Schedule for retry with exponential backoff
            job.setStatus(ScrapeStatus.QUEUE);
            LocalDateTime nextRetryTime = LocalDateTime.now()
                .plusMinutes(schedulingConfig.getMinGapMinutes() * (long) Math.pow(2, job.getRetryCount()));
            job.setNextScheduledTime(nextRetryTime);

            log.info("Scheduled retry {} for job {} (name: {}) at {}",
                job.getRetryCount(), job.getId(), job.getName(), nextRetryTime);
        }

        fileBasedJobService.saveScrapeJob(job);
    }

    /**
     * Calculate the next available time slot considering minimum gaps and existing schedules
     */
    private LocalDateTime calculateNextAvailableSlot() {
        LocalDateTime currentTime = LocalDateTime.now();

        // Find the most recent processed item
        return fileBasedJobService.getMostRecentlyProcessedItem()
            .map(recentItem -> {
                LocalDateTime lastProcessedTime = recentItem.getLastProcessedTime();
                if (lastProcessedTime != null) {
                    LocalDateTime minNextTime = lastProcessedTime.plusMinutes(schedulingConfig.getMinGapMinutes());
                    // If the minimum next time is in the future, use it; otherwise use current time
                    LocalDateTime baseTime = minNextTime.isAfter(currentTime) ? minNextTime : currentTime;
                    // Add random additional minutes
                    int randomMinutes = ThreadLocalRandom.current().nextInt(schedulingConfig.getMaxAdditionalRandomMinutes());
                    LocalDateTime result = baseTime.plusMinutes(randomMinutes);

                    log.debug("Calculated slot based on recent item: lastProcessed={}, minNext={}, base={}, random={}, result={}",
                        lastProcessedTime, minNextTime, baseTime, randomMinutes, result);

                    return result;
                }
                LocalDateTime result = currentTime.plusMinutes(ThreadLocalRandom.current().nextInt(1, 3)); // 1-3 minutes from now
                log.debug("No last processed time, scheduling for: {}", result);
                return result;
            })
            .orElse(currentTime.plusMinutes(ThreadLocalRandom.current().nextInt(1, 3))); // 1-3 minutes from now if no previous items
    }
} 