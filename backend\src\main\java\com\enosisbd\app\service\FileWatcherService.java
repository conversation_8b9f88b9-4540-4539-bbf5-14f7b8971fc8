package com.enosisbd.app.service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.enosisbd.app.config.FileWatcherProperties;
import com.enosisbd.app.controller.request.ScrapeRequest;
import com.enosisbd.app.entity.CompanyEntity;
import com.enosisbd.app.entity.ScrapeEntity;
import com.enosisbd.app.entity.ScrapeStatus;
import com.enosisbd.app.model.ScrapeRequestBatch;
import com.enosisbd.app.repository.ScrapeRepository;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class FileWatcherService {
    
    private final FileWatcherProperties fileWatcherProperties;
    private final ScraperService scraperService;
    private final ScrapeRepository scrapeRepository;
    private final ObjectMapper objectMapper;
    
    private final Set<String> processingFiles = Collections.synchronizedSet(new HashSet<>());
    private Semaphore processingLimiter;
    
    @PostConstruct
    public void init() {
        if (fileWatcherProperties.isEnabled()) {
            log.info("File Watcher Service initialized");
            this.processingLimiter = new Semaphore(fileWatcherProperties.getMaxConcurrentFiles());
            createDirectoriesIfNeeded();
            setupObjectMapper();
        } else {
            log.info("File Watcher Service is disabled");
        }
    }
    
    private void setupObjectMapper() {
        objectMapper.registerModule(new JavaTimeModule());
    }
    
    private void createDirectoriesIfNeeded() {
        if (fileWatcherProperties.isAutoCreateFolders()) {
            createDirectory(fileWatcherProperties.getInputFolder());
            createDirectory(fileWatcherProperties.getOutputFolder());
            createDirectory(fileWatcherProperties.getProcessedFolder());
            createDirectory(fileWatcherProperties.getErrorFolder());
        }
    }
    
    private void createDirectory(String path) {
        try {
            Path directory = Paths.get(path);
            if (!Files.exists(directory)) {
                Files.createDirectories(directory);
                log.info("Created directory: {}", path);
            }
        } catch (IOException e) {
            log.error("Failed to create directory: {}", path, e);
        }
    }
    
    @Scheduled(fixedDelayString = "#{${file-watcher.polling-interval-seconds} * 1000}")
    public void watchInputFolder() {
        if (!fileWatcherProperties.isEnabled()) {
            return;
        }
        
        try {
            Path inputPath = Paths.get(fileWatcherProperties.getInputFolder());
            if (!Files.exists(inputPath)) {
                return;
            }
            
            Files.list(inputPath)
                    .filter(Files::isRegularFile)
                    .filter(this::isSupportedFile)
                    .filter(path -> !processingFiles.contains(path.toString()))
                    .forEach(this::processFileAsync);
                    
        } catch (IOException e) {
            log.error("Error while watching input folder", e);
        }
    }
    
    private boolean isSupportedFile(Path path) {
        String fileName = path.getFileName().toString().toLowerCase();
        return fileWatcherProperties.getSupportedExtensions().stream()
                .anyMatch(ext -> fileName.endsWith("." + ext));
    }
    
    private void processFileAsync(Path filePath) {
        String filePathStr = filePath.toString();
        processingFiles.add(filePathStr);
        
        // Check file size before processing
        boolean isLargeFile = isLargeFile(filePath);
        if (isLargeFile) {
            log.warn("Large file detected: {} ({}MB). Processing with special handling.", 
                filePath.getFileName(), getFileSizeMB(filePath));
        }
        
        CompletableFuture<Void> processingTask = CompletableFuture.runAsync(() -> {
            try {
                // Acquire semaphore to limit concurrent processing
                processingLimiter.acquire();
                log.debug("Started processing file: {} (Permits available: {})", 
                    filePath.getFileName(), processingLimiter.availablePermits());
                
                processJsonFile(filePath, isLargeFile);
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("Processing interrupted for file: {}", filePath.getFileName(), e);
                moveToErrorFolder(filePath, "Processing interrupted: " + e.getMessage());
            } catch (Exception e) {
                log.error("Unexpected error processing file: {}", filePath.getFileName(), e);
                moveToErrorFolder(filePath, "Unexpected error: " + e.getMessage());
            } finally {
                processingLimiter.release();
                processingFiles.remove(filePathStr);
                log.debug("Finished processing file: {} (Permits available: {})", 
                    filePath.getFileName(), processingLimiter.availablePermits());
            }
        });
        
        // Add timeout handling
        processingTask.orTimeout(fileWatcherProperties.getProcessingTimeoutMinutes(), TimeUnit.MINUTES)
            .whenComplete((result, throwable) -> {
                if (throwable instanceof TimeoutException) {
                    log.error("Processing timeout for file: {} after {} minutes", 
                        filePath.getFileName(), fileWatcherProperties.getProcessingTimeoutMinutes());
                    moveToErrorFolder(filePath, "Processing timeout after " + 
                        fileWatcherProperties.getProcessingTimeoutMinutes() + " minutes");
                    processingFiles.remove(filePathStr);
                } else if (throwable != null) {
                    log.error("Processing failed for file: {}", filePath.getFileName(), throwable);
                }
            });
    }
    
    private boolean isLargeFile(Path filePath) {
        try {
            long fileSizeBytes = Files.size(filePath);
            long fileSizeMB = fileSizeBytes / (1024 * 1024);
            return fileSizeMB > fileWatcherProperties.getLargeFileThresholdMb();
        } catch (IOException e) {
            log.warn("Could not determine file size for: {}", filePath, e);
            return false;
        }
    }
    
    private long getFileSizeMB(Path filePath) {
        try {
            long fileSizeBytes = Files.size(filePath);
            return fileSizeBytes / (1024 * 1024);
        } catch (IOException e) {
            log.warn("Could not determine file size for: {}", filePath, e);
            return 0;
        }
    }
    
    private void processJsonFile(Path filePath, boolean isLargeFile) {
        String fileName = filePath.getFileName().toString();
        log.info("Processing file: {} (Large file: {})", fileName, isLargeFile);
        
        // Disable auto-commit for large files if configured
        boolean originalAutoCommit = true;
        if (isLargeFile && fileWatcherProperties.isDisableAutoCommitForLargeFiles()) {
            log.info("Disabling auto-commit for large file: {}", fileName);
            // Note: In a real application, you would disable auto-commit on your database connection
            // This is a placeholder for the actual implementation
            originalAutoCommit = false;
        }
        
        try {
            // Read and parse JSON file
            ScrapeRequestBatch batch = parseJsonFile(filePath);
            if (batch == null || batch.getScrapeRequests() == null || batch.getScrapeRequests().isEmpty()) {
                log.warn("No valid scrape requests found in file: {}", fileName);
                moveToErrorFolder(filePath, "No valid scrape requests found");
                return;
            }
            
            // Set batch metadata
            batch.setBatchId(generateBatchId(fileName));
            batch.setDescription("Batch processed from file: " + fileName + (isLargeFile ? " (Large File)" : ""));
            
            log.info("Found {} scrape requests in file: {}", batch.getTotalRequests(), fileName);
            
            // Process each scrape request
            List<Long> createdScrapeIds = new ArrayList<>();
            List<String> processingErrors = new ArrayList<>();
            
            for (ScrapeRequest request : batch.getScrapeRequests()) {
                try {
                    scraperService.saveScrapeRequest(request);
                    // Assuming the service returns or we can find the created entity
                    ScrapeEntity createdEntity = findMostRecentScrapeEntity();
                    if (createdEntity != null) {
                        createdScrapeIds.add(createdEntity.getId());
                    }
                    log.debug("Created scrape entity for company: {}", request.getName());
                } catch (Exception e) {
                    String error = "Failed to save scrape request for company: " + request.getName() + " - " + e.getMessage();
                    log.error(error, e);
                    processingErrors.add(error);
                }
            }
            
            // Wait for scraping completion and save results
            if (!createdScrapeIds.isEmpty()) {
                log.info("Waiting for scraping completion for {} entities from file: {}", createdScrapeIds.size(), fileName);
                waitForScrapingCompletionAndSave(createdScrapeIds, batch.getBatchId(), isLargeFile);
            }
            
            // Create batch summary with processing errors
            createBatchSummary(batch, createdScrapeIds, fileName, processingErrors);
            
            // Manual commit for large files
            if (isLargeFile && fileWatcherProperties.isDisableAutoCommitForLargeFiles()) {
                log.info("Manually committing transaction for large file: {}", fileName);
                // Note: In a real application, you would commit the transaction here
                // This is a placeholder for the actual implementation
            }
            
            // Move processed file
            moveToProcessedFolder(filePath);
            
            log.info("Successfully processed file: {} with {} requests ({} errors)", 
                fileName, batch.getTotalRequests(), processingErrors.size());
            
        } catch (Exception e) {
            log.error("Error processing file: {}", fileName, e);
            moveToErrorFolder(filePath, e.getMessage());
        } finally {
            // Re-enable auto-commit if it was disabled
            if (isLargeFile && fileWatcherProperties.isDisableAutoCommitForLargeFiles() && !originalAutoCommit) {
                log.info("Re-enabling auto-commit after processing large file: {}", fileName);
                // Note: In a real application, you would re-enable auto-commit here
            }
        }
    }
    
    private ScrapeRequestBatch parseJsonFile(Path filePath) throws IOException {
        // Try to parse as ScrapeRequestBatch first
        try {
            return objectMapper.readValue(filePath.toFile(), ScrapeRequestBatch.class);
        } catch (Exception e) {
            log.debug("Failed to parse as ScrapeRequestBatch, trying as List<ScrapeRequest>");
        }
        
        // Try to parse as direct list of ScrapeRequests
        try {
            List<ScrapeRequest> requests = objectMapper.readValue(
                filePath.toFile(), 
                new TypeReference<List<ScrapeRequest>>() {}
            );
            return new ScrapeRequestBatch(requests);
        } catch (Exception e) {
            log.error("Failed to parse JSON file as either ScrapeRequestBatch or List<ScrapeRequest>: {}", filePath, e);
            throw e;
        }
    }
    
    private ScrapeEntity findMostRecentScrapeEntity() {
        // This is a simple approach - in production you might want a better way to track created entities
        return scrapeRepository.findMostRecentlyProcessedItem().orElse(null);
    }
    
    private String generateBatchId(String fileName) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String cleanFileName = fileName.replaceAll("[^a-zA-Z0-9._-]", "_");
        return "batch_" + cleanFileName + "_" + timestamp;
    }
    
    private void waitForScrapingCompletionAndSave(List<Long> scrapeIds, String batchId, boolean isLargeFile) {
        int maxWaitMinutes = isLargeFile ? 10 : 5; // Wait longer for large files
        int checkIntervalSeconds = 30;
        int maxChecks = (maxWaitMinutes * 60) / checkIntervalSeconds;
        
        for (int check = 0; check < maxChecks; check++) {
            try {
                Thread.sleep(checkIntervalSeconds * 1000);
                
                // Check completion status
                int completedCount = 0;
                int failedCount = 0;
                
                for (Long scrapeId : scrapeIds) {
                    Optional<ScrapeEntity> entityOpt = scrapeRepository.findById(scrapeId);
                    if (entityOpt.isPresent()) {
                        ScrapeEntity entity = entityOpt.get();
                        if (entity.getStatus() == ScrapeStatus.SUCCESS) {
                            completedCount++;
                            // Save completed scrape data immediately
                            if (entity.getCompanyEntity() != null) {
                                saveCompletedScrapeData(entity, batchId);
                            }
                        } else if (entity.getStatus() == ScrapeStatus.FAILED) {
                            failedCount++;
                        }
                    }
                }
                
                int totalProcessed = completedCount + failedCount;
                log.info("Scraping progress for batch {}: {}/{} completed, {} successful, {} failed", 
                    batchId, totalProcessed, scrapeIds.size(), completedCount, failedCount);
                
                // If all are completed or failed, break
                if (totalProcessed >= scrapeIds.size()) {
                    log.info("All scraping completed for batch: {}", batchId);
                    break;
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Scraping completion wait interrupted for batch: {}", batchId);
                break;
            }
        }
    }
    
    private void saveCompletedScrapeData(ScrapeEntity scrapeEntity, String batchId) {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = String.format("company_%s_%s_%s_%s.json", 
                sanitizeFileName(scrapeEntity.getCompanyEntity().getName()),
                scrapeEntity.getId(),
                batchId,
                timestamp);
            
            Path outputPath = Paths.get(fileWatcherProperties.getOutputFolder(), fileName);
            
            // Check if file already exists to avoid duplicates
            if (Files.exists(outputPath)) {
                return;
            }
            
            Map<String, Object> exportData = new HashMap<>();
            exportData.put("scrapeId", scrapeEntity.getId());
            exportData.put("batchId", batchId);
            exportData.put("exportedAt", LocalDateTime.now());
            exportData.put("scrapeStatus", scrapeEntity.getStatus());
            exportData.put("lastProcessedTime", scrapeEntity.getLastProcessedTime());
            exportData.put("companyData", scrapeEntity.getCompanyEntity());
            Map<String, Object> originalEntity = new HashMap<>();
            originalEntity.put("id", scrapeEntity.getId());
            originalEntity.put("name", scrapeEntity.getName());
            originalEntity.put("glassdoor", scrapeEntity.getGlassdoor());
            originalEntity.put("clutch", scrapeEntity.getClutch());
            originalEntity.put("goodfirms", scrapeEntity.getGoodfirms());
            originalEntity.put("website", scrapeEntity.getWebsite());
            originalEntity.put("linkedin", scrapeEntity.getLinkedin());
            originalEntity.put("status", scrapeEntity.getStatus());
            originalEntity.put("retryCount", scrapeEntity.getRetryCount());
            originalEntity.put("createdAt", scrapeEntity.getCreatedAt());
            originalEntity.put("updatedAt", scrapeEntity.getUpdatedAt());
            exportData.put("originalScrapeEntity", originalEntity);
            
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(outputPath.toFile(), exportData);
            log.info("Saved completed scrape data: {}", fileName);
            
        } catch (Exception e) {
            log.error("Failed to save completed scrape data for: {}", scrapeEntity.getName(), e);
        }
    }
    
    private void createBatchSummary(ScrapeRequestBatch batch, List<Long> createdScrapeIds, String fileName, List<String> processingErrors) {
        try {
            Map<String, Object> summary = new HashMap<>();
            summary.put("batchId", batch.getBatchId());
            summary.put("originalFileName", fileName);
            summary.put("processedAt", LocalDateTime.now());
            summary.put("totalRequests", batch.getTotalRequests());
            summary.put("successfullyCreated", createdScrapeIds.size());
            summary.put("failedRequests", processingErrors.size());
            summary.put("createdScrapeIds", createdScrapeIds);
            summary.put("processingErrors", processingErrors);
            summary.put("description", batch.getDescription());
            
            String summaryFileName = "summary_" + batch.getBatchId() + ".json";
            Path summaryPath = Paths.get(fileWatcherProperties.getOutputFolder(), summaryFileName);
            
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(summaryPath.toFile(), summary);
            log.info("Created batch summary: {} (Successful: {}, Failed: {})", 
                summaryFileName, createdScrapeIds.size(), processingErrors.size());
            
        } catch (Exception e) {
            log.error("Failed to create batch summary for file: {}", fileName, e);
        }
    }
    
    private void moveToProcessedFolder(Path filePath) {
        moveFile(filePath, fileWatcherProperties.getProcessedFolder(), "processed");
    }
    
    private void moveToErrorFolder(Path filePath, String errorMessage) {
        try {
            // Create error info file
            String errorFileName = filePath.getFileName().toString() + ".error";
            Path errorInfoPath = Paths.get(fileWatcherProperties.getErrorFolder(), errorFileName);
            
            Map<String, Object> errorInfo = Map.of(
                "originalFile", filePath.getFileName().toString(),
                "errorMessage", errorMessage,
                "errorTime", LocalDateTime.now()
            );
            
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(errorInfoPath.toFile(), errorInfo);
        } catch (Exception e) {
            log.error("Failed to create error info file", e);
        }
        
        moveFile(filePath, fileWatcherProperties.getErrorFolder(), "error");
    }
    
    private void moveFile(Path sourcePath, String targetFolder, String operation) {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String originalFileName = sourcePath.getFileName().toString();
            String newFileName = timestamp + "_" + originalFileName;
            
            Path targetPath = Paths.get(targetFolder, newFileName);
            Files.move(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
            log.info("Moved file to {} folder: {} -> {}", operation, originalFileName, newFileName);
            
        } catch (IOException e) {
            log.error("Failed to move file to {} folder: {}", operation, sourcePath, e);
        }
    }
    
    /**
     * Monitor and export completed scrape results to output folder
     * This is a fallback for scrapes not processed through file watcher
     */
    @Scheduled(fixedDelayString = "#{60 * 1000}") // Check every minute
    public void exportCompletedScrapes() {
        if (!fileWatcherProperties.isEnabled()) {
            return;
        }
        
        try {
            List<ScrapeEntity> completedScrapes = scrapeRepository.findByStatus(ScrapeStatus.SUCCESS);
            
            for (ScrapeEntity scrapeEntity : completedScrapes) {
                if (scrapeEntity.getCompanyEntity() != null) {
                    // Only export if not already exported (fallback for non-file-watcher scrapes)
                    exportCompanyDataFallback(scrapeEntity.getCompanyEntity(), scrapeEntity);
                }
            }
            
        } catch (Exception e) {
            log.error("Error while exporting completed scrapes", e);
        }
    }
    
    private void exportCompanyDataFallback(CompanyEntity company, ScrapeEntity scrapeEntity) {
        try {
            // Check if file was already exported through file watcher processing
            String pattern = String.format("company_%s_%s_*", 
                sanitizeFileName(company.getName()), 
                scrapeEntity.getId());
            
            Path outputDir = Paths.get(fileWatcherProperties.getOutputFolder());
            if (Files.exists(outputDir)) {
                boolean alreadyExported = Files.list(outputDir)
                    .anyMatch(path -> path.getFileName().toString().matches(
                        pattern.replace("*", ".*")));
                
                if (alreadyExported) {
                    log.debug("Company data already exported for: {} (ID: {})", company.getName(), scrapeEntity.getId());
                    return;
                }
            }
            
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = String.format("company_%s_%s_fallback_%s.json", 
                sanitizeFileName(company.getName()), 
                scrapeEntity.getId(),
                timestamp);
            
            Path outputPath = Paths.get(fileWatcherProperties.getOutputFolder(), fileName);
            
            Map<String, Object> exportData = new HashMap<>();
            exportData.put("scrapeId", scrapeEntity.getId());
            exportData.put("exportedAt", LocalDateTime.now());
            exportData.put("scrapeStatus", scrapeEntity.getStatus());
            exportData.put("lastProcessedTime", scrapeEntity.getLastProcessedTime());
            exportData.put("companyData", company);
            exportData.put("exportType", "fallback");
            exportData.put("note", "Exported via fallback mechanism (not through file watcher)");
            
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(outputPath.toFile(), exportData);
            log.info("Exported company data (fallback): {}", fileName);
            
        } catch (Exception e) {
            log.error("Failed to export company data for: {}", company.getName(), e);
        }
    }
    
    private String sanitizeFileName(String fileName) {
        if (fileName == null) {
            return "unknown";
        }
        return fileName.replaceAll("[^a-zA-Z0-9._-]", "_").substring(0, Math.min(fileName.length(), 50));
    }
} 