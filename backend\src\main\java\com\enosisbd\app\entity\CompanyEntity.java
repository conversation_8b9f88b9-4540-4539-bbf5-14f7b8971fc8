package com.enosisbd.app.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
@Table(name = "companies")
public class CompanyEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "slug")
    private String clutchSlug;
    
    @Column(name = "provider_name")
    private String clutchProviderName;
    
    @Column(name = "company_logo_url")
    private String clutchCompanyLogoURL;
    
    @Column(name = "company_overview", columnDefinition = "TEXT")
    private String clutchCompanyOverview;
    
    @Column(name = "company_url")
    private String clutchCompanyUrl;
    
    @Column(name = "profile_url")
    private String clutchProfileUrl;
    
    @Column(name = "total_reviews")
    private Integer clutchTotalReviews;
    
    @Column(name = "minimum_project_size")
    private Integer clutchMinimumProjectSize;
    
    @Column(name = "hourly_rate_range")
    private String clutchHourlyRateRange;
    
    @Column(name = "city")
    private String clutchCity;
    
    @Column(name = "country")
    private String clutchCountry;
    
    // Rating information fields
    @Column(name = "overall_rating")
    private Double clutchClutchRating;
    
    @Column(name = "quality_rating")
    private String clutchQualityRating;
    
    @Column(name = "schedule_rating")
    private String clutchScheduleRating;
    
    @Column(name = "cost_rating")
    private String clutchCostRating;
    
    @Column(name = "willing_to_refer")
    private Boolean clutchWillingToRefer;
    
    // Verification information
    @Column(name = "verification_badge")
    private Boolean clutchVerificationBadge;
    
    @Column(name = "verified")
    private Boolean clutchVerified;
    
    @Column(name = "verification_badge_text")
    private String clutchVerificationBadgeText;
    
    // Services and industries (stored as JSON or comma-separated)
    @Column(name = "all_services", columnDefinition = "TEXT")
    private String clutchAllServices;
    
    @Column(name = "all_industries", columnDefinition = "TEXT")
    private String clutchAllIndustries;
    
    @Column(name = "top_mentions", columnDefinition = "TEXT")
    private String topMentions;
    
    // Clutch-related fields (legacy/additional)
    @Column(name = "clutch_status")
    private Boolean clutchStatus = false;
    
    @Column(name = "clutch_rating")
    private Double clutchRating;
    
    @Column(name = "clutch_reviews_count")
    private Integer clutchReviewsCount;
    
    @Column(name = "clutch_location")
    private String clutchLocation;
    
    @Column(name = "clutch_employees")
    private String clutchEmployees;
    
    @Column(name = "clutch_hourly_rate")
    private String clutchHourlyRate;
    
    @Column(name = "clutch_min_project_size")
    private String clutchMinProjectSize;
    
    // Goodfirms-related fields
    @Column(name = "goodfirms_status")
    private Boolean goodfirmsStatus = false;
    
    @Column(name = "goodfirms_rating")
    private Double goodfirmsRating;
    
    @Column(name = "goodfirms_reviews_count")
    private Integer goodfirmsReviewsCount;
    
    @Column(name = "goodfirms_location")
    private String goodfirmsLocation;
    
    @Column(name = "goodfirms_employees")
    private String goodfirmsEmployees;
    
    @Column(name = "goodfirms_services", columnDefinition = "TEXT")
    private String goodfirmsServices;
    
    // Client feedback from Goodfirms
    @Column(name = "goodfirms_client_likes", columnDefinition = "TEXT")
    private String goodfirmsClientLikes;
    
    @Column(name = "goodfirms_client_dislikes", columnDefinition = "TEXT")
    private String goodfirmsClientDislikes;
    
    @Column(name = "goodfirms_provider_name")
    private String goodfirmsProviderName;
    
    @Column(name = "goodfirms_profile_url")
    private String goodfirmsProfileUrl;
    
    @Column(name = "goodfirms_verification_badge")
    private Boolean goodfirmsVerificationBadge;
    
    // Glassdoor-related fields
    @Column(name = "glassdoor_status")
    private Boolean glassdoorStatus = false;
    
    @Column(name = "glassdoor_rating")
    private Double glassdoorRating;
    
    @Column(name = "glassdoor_reviews_count")
    private Integer glassdoorReviewsCount;
    
    @Column(name = "glassdoor_employee_satisfaction")
    private Double glassdoorEmployeeSatisfaction;
    
    // Additional Glassdoor-specific fields
    @Column(name = "glassdoor_category_ratings", columnDefinition = "TEXT")
    private String glassdoorCategoryRatings; // JSON string of category ratings
    
    @Column(name = "glassdoor_ratings_distribution", columnDefinition = "TEXT")
    private String glassdoorRatingsDistribution; // JSON string of ratings distribution
    
    @Column(name = "glassdoor_pros", columnDefinition = "TEXT")
    private String glassdoorPros; // Semicolon-separated pros
    
    @Column(name = "glassdoor_cons", columnDefinition = "TEXT")
    private String glassdoorCons; // Semicolon-separated cons
    
    // LinkedIn-related fields
    @Column(name = "linkedin_status")
    private Boolean linkedinStatus = false;
    
    @Column(name = "linkedin_followers")
    private Integer linkedinFollowers;
    
    @Column(name = "linkedin_employees_count")
    private Integer linkedinEmployeesCount;
    
    @Column(name = "linkedin_industry")
    private String linkedinIndustry;
    
    // Website-related fields
    @Column(name = "website_status")
    private Boolean websiteStatus = false;
    
    @Column(name = "website_title")
    private String websiteTitle;
    
    @Column(name = "website_description", columnDefinition = "TEXT")
    private String websiteDescription;
    
    @Column(name = "website_keywords", columnDefinition = "TEXT")
    private String websiteKeywords;
    
    // New website-specific company data fields
    @Column(name = "website_name")
    private String websiteName;
    
    @Column(name = "website_url")
    private String websiteUrl;
    
    @Column(name = "website_founded_year")
    private String websiteFoundedYear;
    
    @Column(name = "website_employee_size")
    private String websiteEmployeeSize;
    
    @Column(name = "website_headquarters_location")
    private String websiteHeadquartersLocation;
    
    @Column(name = "website_city")
    private String websiteCity;
    
    @Column(name = "website_country")
    private String websiteCountry;
    
    @Column(name = "website_focus_statement", columnDefinition = "TEXT")
    private String websiteFocusStatement;
    
    @Column(name = "website_culture_description", columnDefinition = "TEXT")
    private String websiteCultureDescription;
    
    @Column(name = "website_outcome_summary", columnDefinition = "TEXT")
    private String websiteOutcomeSummary;
    
    @Column(name = "website_industries", columnDefinition = "TEXT")
    private String websiteIndustries;
    
    @Column(name = "website_services", columnDefinition = "TEXT")
    private String websiteServices;
    
    @Column(name = "website_service_details", columnDefinition = "TEXT")
    private String websiteServiceDetails;
    
    @Column(name = "website_achievements", columnDefinition = "TEXT")
    private String websiteAchievements;
    
    @Column(name = "website_founding_story", columnDefinition = "TEXT")
    private String websiteFoundingStory;
    
    @Column(name = "website_global_presence", columnDefinition = "TEXT")
    private String websiteGlobalPresence;
    
    @Column(name = "website_initiatives", columnDefinition = "TEXT")
    private String websiteInitiatives;
    
    @Column(name = "website_aliases", columnDefinition = "TEXT")
    private String websiteAliases;
    
    @Column(name = "website_funding_status")
    private String websiteFundingStatus;
    
    // General company information
    @Column(name = "company_description", columnDefinition = "TEXT")
    private String clutchCompanyDescription;
    
    @Column(name = "founded_year")
    private String clutchFoundedYear;
    
    @Column(name = "company_size")
    private String clutchCompanySize;

    @Column(name = "industry")
    private String clutchIndustry;
    
    @Column(name = "specialties", columnDefinition = "TEXT")
    private String clutchSpecialties;
    
    // Processing status
    @Column(name = "data_completeness_score")
    private Double dataCompletenessScore;
    
    @Column(name = "last_scraped")
    private LocalDateTime lastScraped;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;
}
