2025-06-29 03:58:00,758 ERROR c.e.a.s.i.w.WebsiteCrawlerServiceImpl [ForkJoinPool.commonPool-worker-2] Error scraping website: https://www.darly.solutions
feign.RetryableException: Read timed out executing POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key=AIzaSyBZW8zg-8g8Uul33M5zUm8n-zCfXwkBjaA
	at feign.FeignException.errorExecuting(FeignException.java:300)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:105)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:53)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:104)
	at jdk.proxy2/jdk.proxy2.$Proxy199.generateContent(Unknown Source)
	at com.enosisbd.app.service.impl.website.WebsiteCrawlerServiceImpl.scrapeFromWebsite(WebsiteCrawlerServiceImpl.java:96)
	at com.enosisbd.app.flowable.DataExtractionDelegate.lambda$performDataExtraction$4(DataExtractionDelegate.java:139)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:280)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:306)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:347)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:800)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:966)
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:478)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:472)
	at java.base/sun.security.ssl.SSLSocketInputRecord.bytesInCompletePacket(SSLSocketInputRecord.java:70)
	at java.base/sun.security.ssl.SSLSocketImpl.readApplicationRecord(SSLSocketImpl.java:1455)
	at java.base/sun.security.ssl.SSLSocketImpl$AppInputStream.read(SSLSocketImpl.java:1059)
	at java.base/java.io.BufferedInputStream.fill(BufferedInputStream.java:244)
	at java.base/java.io.BufferedInputStream.read1(BufferedInputStream.java:284)
	at java.base/java.io.BufferedInputStream.read(BufferedInputStream.java:343)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:791)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:726)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at java.base/sun.net.www.protocol.https.HttpsURLConnectionImpl.getResponseCode(HttpsURLConnectionImpl.java:308)
	at feign.Client$Default.convertResponse(Client.java:114)
	at feign.Client$Default.execute(Client.java:110)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:92)
	... 12 common frames omitted
2025-06-29 03:58:01,823 ERROR c.e.a.s.ScrapeScheduledJob [scheduling-1] Error in processScheduledItems job: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]; SQL [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
org.springframework.dao.CannotAcquireLockException: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]; SQL [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:287)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:256)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:244)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:566)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:795)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:698)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:416)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.enosisbd.app.service.impl.ScrapeSchedulerServiceImpl$$SpringCGLIB$$0.processScheduledItems(<generated>)
	at com.enosisbd.app.scheduler.ScrapeScheduledJob.processScheduledItems(ScrapeScheduledJob.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124)
	at io.micrometer.observation.Observation.observe(Observation.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124)
	at org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.hibernate.exception.LockAcquisitionException: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:108)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:134)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:55)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:55)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.doStaticUpdate(UpdateCoordinatorStandard.java:781)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.performUpdate(UpdateCoordinatorStandard.java:328)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.update(UpdateCoordinatorStandard.java:245)
	at org.hibernate.action.internal.EntityUpdateAction.execute(EntityUpdateAction.java:169)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:644)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1429)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:491)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2354)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1978)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.beforeCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:169)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.commit(JdbcResourceLocalTransactionCoordinatorImpl.java:267)
	at org.hibernate.engine.transaction.internal.TransactionImpl.commit(TransactionImpl.java:101)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:562)
	... 23 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: could not serialize access due to concurrent update
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2734)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2421)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:372)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:518)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:435)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:196)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:157)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 44 common frames omitted
2025-06-29 12:06:08,852 ERROR c.e.a.s.i.w.WebsiteCrawlerServiceImpl [ForkJoinPool.commonPool-worker-3] Error scraping website: https://luby.co
feign.RetryableException: Read timed out executing POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key=AIzaSyBZW8zg-8g8Uul33M5zUm8n-zCfXwkBjaA
	at feign.FeignException.errorExecuting(FeignException.java:300)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:105)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:53)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:104)
	at jdk.proxy2/jdk.proxy2.$Proxy199.generateContent(Unknown Source)
	at com.enosisbd.app.service.impl.website.WebsiteCrawlerServiceImpl.scrapeFromWebsite(WebsiteCrawlerServiceImpl.java:96)
	at com.enosisbd.app.flowable.DataExtractionDelegate.lambda$4(DataExtractionDelegate.java:139)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:280)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:306)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:347)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:800)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:966)
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:478)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:472)
	at java.base/sun.security.ssl.SSLSocketInputRecord.bytesInCompletePacket(SSLSocketInputRecord.java:70)
	at java.base/sun.security.ssl.SSLSocketImpl.readApplicationRecord(SSLSocketImpl.java:1455)
	at java.base/sun.security.ssl.SSLSocketImpl$AppInputStream.read(SSLSocketImpl.java:1059)
	at java.base/java.io.BufferedInputStream.fill(BufferedInputStream.java:244)
	at java.base/java.io.BufferedInputStream.read1(BufferedInputStream.java:284)
	at java.base/java.io.BufferedInputStream.read(BufferedInputStream.java:343)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:791)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:726)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at java.base/sun.net.www.protocol.https.HttpsURLConnectionImpl.getResponseCode(HttpsURLConnectionImpl.java:308)
	at feign.Client$Default.convertResponse(Client.java:114)
	at feign.Client$Default.execute(Client.java:110)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:92)
	... 12 common frames omitted
2025-06-29 12:06:10,029 ERROR c.e.a.s.ScrapeScheduledJob [scheduling-1] Error in processScheduledItems job: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]; SQL [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
org.springframework.dao.CannotAcquireLockException: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]; SQL [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:287)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:256)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:244)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:566)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:795)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:698)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:416)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.enosisbd.app.service.impl.ScrapeSchedulerServiceImpl$$SpringCGLIB$$0.processScheduledItems(<generated>)
	at com.enosisbd.app.scheduler.ScrapeScheduledJob.processScheduledItems(ScrapeScheduledJob.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124)
	at io.micrometer.observation.Observation.observe(Observation.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124)
	at org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.hibernate.exception.LockAcquisitionException: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:108)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:134)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:55)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:55)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.doStaticUpdate(UpdateCoordinatorStandard.java:781)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.performUpdate(UpdateCoordinatorStandard.java:328)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.update(UpdateCoordinatorStandard.java:245)
	at org.hibernate.action.internal.EntityUpdateAction.execute(EntityUpdateAction.java:169)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:644)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1429)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:491)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2354)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1978)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.beforeCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:169)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.commit(JdbcResourceLocalTransactionCoordinatorImpl.java:267)
	at org.hibernate.engine.transaction.internal.TransactionImpl.commit(TransactionImpl.java:101)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:562)
	... 23 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: could not serialize access due to concurrent update
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2734)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2421)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:372)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:518)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:435)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:196)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:157)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 44 common frames omitted
2025-06-29 12:06:25,227 ERROR c.e.a.s.i.w.WebsiteCrawlerServiceImpl [ForkJoinPool.commonPool-worker-2] Error scraping website: https://www.darly.solutions
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [POST] to [https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key=AIzaSyBZW8zg-8g8Uul33M5zUm8n-zCfXwkBjaA] [GeminiClient#generateContent(String,String,GeminiRequest)]: [{
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}
]
	at feign.FeignException.serverErrorStatus(FeignException.java:287)
	at feign.FeignException.errorStatus(FeignException.java:226)
	at feign.FeignException.errorStatus(FeignException.java:213)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:103)
	at feign.InvocationContext.decodeError(InvocationContext.java:133)
	at feign.InvocationContext.proceed(InvocationContext.java:80)
	at feign.ResponseHandler.handleResponse(ResponseHandler.java:69)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:109)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:53)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:104)
	at jdk.proxy2/jdk.proxy2.$Proxy199.generateContent(Unknown Source)
	at com.enosisbd.app.service.impl.website.WebsiteCrawlerServiceImpl.scrapeFromWebsite(WebsiteCrawlerServiceImpl.java:96)
	at com.enosisbd.app.flowable.DataExtractionDelegate.lambda$4(DataExtractionDelegate.java:139)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
2025-06-29 12:06:57,356 ERROR c.e.a.s.ScrapeScheduledJob [scheduling-1] Error in processScheduledItems job: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]; SQL [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
org.springframework.dao.CannotAcquireLockException: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]; SQL [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:287)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:256)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:244)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:566)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:795)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:698)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:416)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.enosisbd.app.service.impl.ScrapeSchedulerServiceImpl$$SpringCGLIB$$0.processScheduledItems(<generated>)
	at com.enosisbd.app.scheduler.ScrapeScheduledJob.processScheduledItems(ScrapeScheduledJob.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124)
	at io.micrometer.observation.Observation.observe(Observation.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124)
	at org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.hibernate.exception.LockAcquisitionException: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:108)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:134)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:55)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:55)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.doStaticUpdate(UpdateCoordinatorStandard.java:781)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.performUpdate(UpdateCoordinatorStandard.java:328)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.update(UpdateCoordinatorStandard.java:245)
	at org.hibernate.action.internal.EntityUpdateAction.execute(EntityUpdateAction.java:169)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:644)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1429)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:491)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2354)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1978)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.beforeCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:169)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.commit(JdbcResourceLocalTransactionCoordinatorImpl.java:267)
	at org.hibernate.engine.transaction.internal.TransactionImpl.commit(TransactionImpl.java:101)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:562)
	... 23 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: could not serialize access due to concurrent update
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2734)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2421)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:372)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:518)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:435)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:196)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:157)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 44 common frames omitted
2025-06-29 13:52:22,605 ERROR c.e.a.s.ScrapeScheduledJob [scheduling-1] Error in processScheduledItems job: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]; SQL [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
org.springframework.dao.CannotAcquireLockException: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]; SQL [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:287)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:256)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:244)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:566)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:795)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:698)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:416)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.enosisbd.app.service.impl.ScrapeSchedulerServiceImpl$$SpringCGLIB$$0.processScheduledItems(<generated>)
	at com.enosisbd.app.scheduler.ScrapeScheduledJob.processScheduledItems(ScrapeScheduledJob.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124)
	at io.micrometer.observation.Observation.observe(Observation.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124)
	at org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.hibernate.exception.LockAcquisitionException: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:108)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:134)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:55)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:55)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.doStaticUpdate(UpdateCoordinatorStandard.java:781)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.performUpdate(UpdateCoordinatorStandard.java:328)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.update(UpdateCoordinatorStandard.java:245)
	at org.hibernate.action.internal.EntityUpdateAction.execute(EntityUpdateAction.java:169)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:644)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1429)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:491)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2354)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1978)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.beforeCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:169)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.commit(JdbcResourceLocalTransactionCoordinatorImpl.java:267)
	at org.hibernate.engine.transaction.internal.TransactionImpl.commit(TransactionImpl.java:101)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:562)
	... 23 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: could not serialize access due to concurrent update
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2734)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2421)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:372)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:518)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:435)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:196)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:157)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 44 common frames omitted
2025-06-29 13:55:07,054 ERROR c.e.a.s.ScrapeScheduledJob [scheduling-1] Error in processScheduledItems job: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]; SQL [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
org.springframework.dao.CannotAcquireLockException: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]; SQL [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:287)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:256)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:244)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:566)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:795)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:698)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:416)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.enosisbd.app.service.impl.ScrapeSchedulerServiceImpl$$SpringCGLIB$$0.processScheduledItems(<generated>)
	at com.enosisbd.app.scheduler.ScrapeScheduledJob.processScheduledItems(ScrapeScheduledJob.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124)
	at io.micrometer.observation.Observation.observe(Observation.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124)
	at org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.hibernate.exception.LockAcquisitionException: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:108)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:134)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:55)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:55)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.doStaticUpdate(UpdateCoordinatorStandard.java:781)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.performUpdate(UpdateCoordinatorStandard.java:328)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.update(UpdateCoordinatorStandard.java:245)
	at org.hibernate.action.internal.EntityUpdateAction.execute(EntityUpdateAction.java:169)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:644)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1429)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:491)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2354)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1978)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.beforeCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:169)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.commit(JdbcResourceLocalTransactionCoordinatorImpl.java:267)
	at org.hibernate.engine.transaction.internal.TransactionImpl.commit(TransactionImpl.java:101)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:562)
	... 23 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: could not serialize access due to concurrent update
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2734)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2421)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:372)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:518)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:435)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:196)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:157)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 44 common frames omitted
2025-06-29 14:17:40,293 ERROR c.e.a.s.i.w.WebsiteCrawlerServiceImpl [ForkJoinPool.commonPool-worker-3] Error scraping website: https://luby.co
feign.RetryableException: Read timed out executing POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key=AIzaSyBZW8zg-8g8Uul33M5zUm8n-zCfXwkBjaA
	at feign.FeignException.errorExecuting(FeignException.java:300)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:105)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:53)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:104)
	at jdk.proxy2/jdk.proxy2.$Proxy199.generateContent(Unknown Source)
	at com.enosisbd.app.service.impl.website.WebsiteCrawlerServiceImpl.scrapeFromWebsite(WebsiteCrawlerServiceImpl.java:96)
	at com.enosisbd.app.flowable.DataExtractionDelegate.lambda$4(DataExtractionDelegate.java:139)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:280)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:306)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:347)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:800)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:966)
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:478)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:472)
	at java.base/sun.security.ssl.SSLSocketInputRecord.bytesInCompletePacket(SSLSocketInputRecord.java:70)
	at java.base/sun.security.ssl.SSLSocketImpl.readApplicationRecord(SSLSocketImpl.java:1455)
	at java.base/sun.security.ssl.SSLSocketImpl$AppInputStream.read(SSLSocketImpl.java:1059)
	at java.base/java.io.BufferedInputStream.fill(BufferedInputStream.java:244)
	at java.base/java.io.BufferedInputStream.read1(BufferedInputStream.java:284)
	at java.base/java.io.BufferedInputStream.read(BufferedInputStream.java:343)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:791)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:726)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at java.base/sun.net.www.protocol.https.HttpsURLConnectionImpl.getResponseCode(HttpsURLConnectionImpl.java:308)
	at feign.Client$Default.convertResponse(Client.java:114)
	at feign.Client$Default.execute(Client.java:110)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:92)
	... 12 common frames omitted
2025-06-29 14:17:41,424 ERROR c.e.a.s.ScrapeScheduledJob [scheduling-1] Error in processScheduledItems job: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]; SQL [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
org.springframework.dao.CannotAcquireLockException: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]; SQL [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:287)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:256)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:244)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:566)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:795)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:698)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:416)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.enosisbd.app.service.impl.ScrapeSchedulerServiceImpl$$SpringCGLIB$$0.processScheduledItems(<generated>)
	at com.enosisbd.app.scheduler.ScrapeScheduledJob.processScheduledItems(ScrapeScheduledJob.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124)
	at io.micrometer.observation.Observation.observe(Observation.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124)
	at org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.hibernate.exception.LockAcquisitionException: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:108)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:134)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:55)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:55)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.doStaticUpdate(UpdateCoordinatorStandard.java:781)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.performUpdate(UpdateCoordinatorStandard.java:328)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.update(UpdateCoordinatorStandard.java:245)
	at org.hibernate.action.internal.EntityUpdateAction.execute(EntityUpdateAction.java:169)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:644)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1429)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:491)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2354)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1978)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.beforeCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:169)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.commit(JdbcResourceLocalTransactionCoordinatorImpl.java:267)
	at org.hibernate.engine.transaction.internal.TransactionImpl.commit(TransactionImpl.java:101)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:562)
	... 23 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: could not serialize access due to concurrent update
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2734)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2421)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:372)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:518)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:435)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:196)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:157)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 44 common frames omitted
2025-06-29 14:18:23,155 ERROR c.e.a.s.ScrapeScheduledJob [scheduling-1] Error in processScheduledItems job: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]; SQL [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
org.springframework.dao.CannotAcquireLockException: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]; SQL [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:287)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:256)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:244)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:566)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:795)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:698)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:416)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.enosisbd.app.service.impl.ScrapeSchedulerServiceImpl$$SpringCGLIB$$0.processScheduledItems(<generated>)
	at com.enosisbd.app.scheduler.ScrapeScheduledJob.processScheduledItems(ScrapeScheduledJob.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124)
	at io.micrometer.observation.Observation.observe(Observation.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124)
	at org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.hibernate.exception.LockAcquisitionException: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:108)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:134)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:55)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:55)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.doStaticUpdate(UpdateCoordinatorStandard.java:781)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.performUpdate(UpdateCoordinatorStandard.java:328)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.update(UpdateCoordinatorStandard.java:245)
	at org.hibernate.action.internal.EntityUpdateAction.execute(EntityUpdateAction.java:169)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:644)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1429)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:491)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2354)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1978)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.beforeCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:169)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.commit(JdbcResourceLocalTransactionCoordinatorImpl.java:267)
	at org.hibernate.engine.transaction.internal.TransactionImpl.commit(TransactionImpl.java:101)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:562)
	... 23 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: could not serialize access due to concurrent update
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2734)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2421)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:372)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:518)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:435)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:196)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:157)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 44 common frames omitted
